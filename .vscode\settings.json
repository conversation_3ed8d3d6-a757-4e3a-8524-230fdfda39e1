{"[python]": {"editor.defaultFormatter": "charliermarsh.ruff", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.ruff": "explicit", "source.organizeImports.ruff": "explicit"}, "editor.tabSize": 4}, "[javascript][typescript][javascriptreact][typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "editor.renderWhitespace": "all", "editor.insertSpaces": true, "editor.detectIndentation": true, "editor.suggestSelection": "first", "editor.wordWrap": "off", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "terminal.integrated.defaultProfile.windows": "PowerShell", "python.analysis.typeCheckingMode": "basic", "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "editor.rulers": [88, 100], "files.associations": {"*.py": "python", "*.md": "markdown", "*.json": "jsonc", "*.toml": "toml", "*.yaml": "yaml", "*.yml": "yaml"}, "files.exclude": {"**/__pycache__": true, "**/.pytest_cache": true, "**/*.pyc": true, "**/.DS_Store": true, "**/.coverage": true, "**/.ruff_cache": true, "**/htmlcov": true, "**/.venv": true}, "search.exclude": {"**/__pycache__": true, "**/.pytest_cache": true, "**/*.pyc": true, "**/.coverage": true, "**/.ruff_cache": true, "**/htmlcov": true, "**/.venv": true, "**/node_modules": true, "**/dist": true}, "eslint.validate": ["javascript", "typescript", "javascriptreact", "typescriptreact"], "eslint.workingDirectories": ["./web-interface"], "git.enableSmartCommit": true, "git.confirmSync": false, "workbench.colorTheme": "Default Dark+", "workbench.iconTheme": "vs-seti", "editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true, "taipyStudio.gUI.elementsFilePaths": []}