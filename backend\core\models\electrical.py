from sqlalchemy import CheckConstraint, ForeignKey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, CommonColumns, EnumType, SoftDeleteColumns
# from .components import Component
from .enums import CableInstallationMethod, ElectricalNodeType
# from .heat_tracing import ControlCircuit, Pipe, Vessel
# from .project import Project
# from .switchboard import Feeder, Switchboard


class ElectricalNode(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "ElectricalNode"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    node_type: Mapped[ElectricalNodeType] = mapped_column(
        EnumType(ElectricalNodeType), nullable=False
    )
    location_description: Mapped[str | None] = mapped_column(nullable=True)

    related_switchboard_id: Mapped[int | None] = mapped_column(
        ForeignKey("Switchboard.id"), nullable=True
    )
    related_feeder_id: Mapped[int | None] = mapped_column(
        ForeignKey("Feeder.id"), nullable=True
    )
    related_control_circuit_id: Mapped[int | None] = mapped_column(
        ForeignKey("ControlCircuit.id"), nullable=True
    )
    related_pipe_id: Mapped[int | None] = mapped_column(
        ForeignKey("Pipe.id"), nullable=True
    )
    related_vessel_id: Mapped[int | None] = mapped_column(
        ForeignKey("Vessel.id"), nullable=True
    )
    related_component_id: Mapped[int | None] = mapped_column(
        ForeignKey("Component.id"), unique=True, nullable=True
    )  # Enforced 1:1

    voltage_v: Mapped[float | None] = mapped_column(nullable=True)
    power_capacity_kva: Mapped[float | None] = mapped_column(nullable=True)

    # Relationships (using string forward references)
    project: Mapped["Project"] = relationship(back_populates="electrical_nodes")
    related_switchboard: Mapped["Switchboard | None"] = relationship(
        back_populates="electrical_nodes"
    )
    related_feeder: Mapped["Feeder | None"] = relationship(
        back_populates="electrical_nodes"
    )
    related_control_circuit: Mapped["ControlCircuit | None"] = relationship(
        back_populates="electrical_nodes"
    )
    related_pipe: Mapped["Pipe | None"] = relationship(
        back_populates="electrical_nodes"
    )
    related_vessel: Mapped["Vessel | None"] = relationship(
        back_populates="electrical_nodes"
    )
    related_component: Mapped["Component | None"] = relationship(
        back_populates="electrical_node"
    )

    outgoing_routes: Mapped[list["CableRoute"]] = relationship(
        foreign_keys="CableRoute.from_node_id",
        back_populates="from_node",
        cascade="all, delete-orphan",
    )
    incoming_routes: Mapped[list["CableRoute"]] = relationship(
        foreign_keys="CableRoute.to_node_id",
        back_populates="to_node",
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        return f"<ElectricalNode(id={self.id}, name='{self.name}', type='{self.node_type.value}', proj_id={self.project_id})>"


class CableRoute(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "CableRoute"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    from_node_id: Mapped[int] = mapped_column(
        ForeignKey("ElectricalNode.id"), nullable=False
    )
    to_node_id: Mapped[int] = mapped_column(
        ForeignKey("ElectricalNode.id"), nullable=False
    )

    cable_component_id: Mapped[int] = mapped_column(
        ForeignKey("Component.id"), nullable=False
    )

    length_m: Mapped[float] = mapped_column(nullable=False)
    number_of_runs: Mapped[int] = mapped_column(default=1, nullable=False)
    installation_method: Mapped[CableInstallationMethod] = mapped_column(
        EnumType(CableInstallationMethod), nullable=False
    )
    max_ambient_temp_c: Mapped[float | None] = mapped_column(nullable=True)
    min_ambient_temp_c: Mapped[float | None] = mapped_column(nullable=True)

    calculated_voltage_drop_v: Mapped[float | None] = mapped_column(nullable=True)
    calculated_current_capacity_a: Mapped[float | None] = mapped_column(nullable=True)

    # Relationships
    project: Mapped["Project"] = relationship(back_populates="cable_routes")
    from_node: Mapped["ElectricalNode"] = relationship(
        foreign_keys=[from_node_id], back_populates="outgoing_routes"
    )
    to_node: Mapped["ElectricalNode"] = relationship(
        foreign_keys=[to_node_id], back_populates="incoming_routes"
    )
    cable_component: Mapped["Component"] = relationship()

    __table_args__ = (
        UniqueConstraint(
            "project_id",
            "from_node_id",
            "to_node_id",
            "cable_component_id",
            name="uq_cable_route_connection",
        ),
        CheckConstraint("from_node_id != to_node_id", name="chk_different_nodes"),
    )

    def __repr__(self):
        return (
            f"<CableRoute(id={self.id}, name='{self.name}', from='{self.from_node.name}', "
            f"to='{self.to_node.name}', cable='{self.cable_component.name}', length={self.length_m}m)>"
        )
