from sqlalchemy import Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, CommonColumns, EnumType, SoftDeleteColumns
# from .documents import ExportedDocument, ImportedDataRevision
# from .electrical import CableRoute, ElectricalNode
from .enums import InstallationEnvironment
# from .heat_tracing import Pipe, Vessel
# from .switchboard import Switchboard


class Project(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "Project"


    project_number: Mapped[str] = mapped_column(nullable=False) # REMOVE UniqueConstraint from here
    description: Mapped[str | None] = mapped_column(Text, nullable=True)
    designer: Mapped[str | None] = mapped_column(nullable=True)
    min_ambient_temp_c: Mapped[float] = mapped_column(nullable=False)
    max_ambient_temp_c: Mapped[float] = mapped_column(nullable=False)
    desired_maintenance_temp_c: Mapped[float] = mapped_column(nullable=False)
    wind_speed_ms: Mapped[float | None] = mapped_column(nullable=True)
    installation_environment: Mapped[InstallationEnvironment | None] = mapped_column(
        EnumType(InstallationEnvironment), nullable=True
    )
    available_voltages_json: Mapped[str | None] = mapped_column(nullable=True)

    default_cable_manufacturer: Mapped[str | None] = mapped_column(nullable=True)
    default_control_device_manufacturer: Mapped[str | None] = mapped_column(
        nullable=True
    )

    # Relationships (using string forward references for models defined in other files)
    switchboards: Mapped[list["Switchboard"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    pipes: Mapped[list["Pipe"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    vessels: Mapped[list["Vessel"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    imported_data_revisions: Mapped[list["ImportedDataRevision"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    exported_documents: Mapped[list["ExportedDocument"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    electrical_nodes: Mapped[list["ElectricalNode"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    cable_routes: Mapped[list["CableRoute"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )

    __table_args__ = (
        UniqueConstraint("name", name="uq_project_name"),
        UniqueConstraint("project_number", name="uq_project_number"), # ADD UniqueConstraint here
    )

    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}', project_number='{self.project_number}')>"
