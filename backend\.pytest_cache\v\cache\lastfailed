{"tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_from_orm_conversion": true, "tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_read_schema_includes_all_fields": true, "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_includes_essential_fields": true, "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_excludes_detailed_fields": true, "tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_list_response_structure": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project": true}