["tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_name_validation_empty", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_name_validation_whitespace", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_normalization", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_validation_empty", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_validation_invalid_chars", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_temperature_bounds_validation", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_temperature_range_validation", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_valid_project_creation", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_invalid_json", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_negative_voltage", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_not_array", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_valid", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_wind_speed_validation", "tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_empty_list_response", "tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_list_response_structure", "tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_from_orm_conversion", "tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_read_schema_includes_all_fields", "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_excludes_detailed_fields", "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_includes_essential_fields", "tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_empty_update", "tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_update_validation_same_as_create", "tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_valid_partial_update", "tests/test_services/test_project_service.py::TestProjectService::test_create_project_success"]