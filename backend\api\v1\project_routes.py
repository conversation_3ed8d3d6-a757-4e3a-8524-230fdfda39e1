# backend/api/v1/project_routes.py
from fastapi import APIRouter, Depends, HTTPException, Response, status
from sqlalchemy.orm import Session

# Dependency injection for database session (from backend/app.py)
from backend.app import get_db
from backend.core.errors.error_factory import ErrorFactory
from backend.core.errors.exceptions import DataValidationError, ProjectNotFoundError
from backend.core.schemas.error import ErrorResponseSchema
from backend.core.schemas.project_schemas import (
    ProjectCreateSchema,
    ProjectReadSchema,
    ProjectUpdateSchema,
)

# Import services and schemas
from backend.core.services.project_service import ProjectService


# Dependency injection for ProjectService (optional, could be in a common dependency)
def get_project_service(db: Session = Depends(get_db)) -> ProjectService:
    from backend.core.repositories.project_repository import ProjectRepository

    project_repo = ProjectRepository(db)
    return ProjectService(project_repo)


router = APIRouter()


@router.post(
    "/",
    response_model=ProjectReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new project",
    responses={
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid input data",
        },
        status.HTTP_409_CONFLICT: {
            "model": ErrorResponseSchema,
            "description": "Project with this name/number already exists",
        },
    },
)
async def create_project(
    project_data: ProjectCreateSchema,
    project_service: ProjectService = Depends(get_project_service),
):
    try:
        new_project = project_service.create_project(project_data)
        return new_project
    except DataValidationError as e:  # Pydantic validation error from service
        raise HTTPException(
            status_code=e.status_code, detail=e.detail
        )  # Re-raise for global handler to catch
    except Exception as e:  # Catch other service-level exceptions (e.g., from DB)
        # Use your ErrorFactory to create a standardized exception
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.get(
    "/{project_id}",
    response_model=ProjectReadSchema,
    summary="Retrieve project details by ID",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Project not found",
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "model": ErrorResponseSchema,
            "description": "Internal server error",
        },
    },
)
async def get_project(
    project_id: str,  # Using string for UUIDs if you implement them
    project_service: ProjectService = Depends(get_project_service),
):
    try:
        project = project_service.get_project_details(project_id)
        return project
    except ProjectNotFoundError as e:
        # FastAPI's HTTPException will be caught by the global handler
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.put(
    "/{project_id}",
    response_model=ProjectReadSchema,
    summary="Update project details",
    responses={
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid input data",
        },
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Project not found",
        },
    },
)
async def update_project(
    project_id: str,
    project_data: ProjectUpdateSchema,
    project_service: ProjectService = Depends(get_project_service),
):
    try:
        updated_project = project_service.update_project(project_id, project_data)
        return updated_project
    except (DataValidationError, ProjectNotFoundError) as e:
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.delete(
    "/{project_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a project (soft delete)",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Project not found",
        }
    },
)
async def delete_project(
    project_id: str, project_service: ProjectService = Depends(get_project_service)
):
    try:
        # Assume delete_project in service implements soft delete
        project_service.delete_project(project_id)
        return Response(status_code=status.HTTP_204_NO_CONTENT)
    except ProjectNotFoundError as e:
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )
