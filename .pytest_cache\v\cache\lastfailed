{"backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_validation_error": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_duplicate_error": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_id_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_code_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_with_pagination": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_include_deleted": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_invalid_pagination_parameters": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_nonexistent_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_nonexistent_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_already_deleted_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_name": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_project_number": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_description": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_case_insensitive": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_excludes_deleted": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects_empty_db": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_name": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_project_number": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_validation_error": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_duplicate_name_error": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_id": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_code": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_not_found": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_update_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_delete_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_projects_list_success": true}