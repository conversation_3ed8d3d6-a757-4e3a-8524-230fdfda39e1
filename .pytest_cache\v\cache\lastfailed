{"backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_validation_error": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_duplicate_error": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_id_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_code_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_with_pagination": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_include_deleted": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_invalid_pagination_parameters": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_nonexistent_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_nonexistent_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_already_deleted_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_name": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_project_number": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_description": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_case_insensitive": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_excludes_deleted": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects_empty_db": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_name": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_project_number": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_validation_error": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_duplicate_name_error": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_id": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_code": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_not_found": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_update_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_delete_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_projects_list_success": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_valid_component_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_minimal_component_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_empty": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_whitespace": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_normalization": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_category_id_validation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_valid": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_invalid": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_empty_string": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_whitespace_only": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_valid_partial_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_empty_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_update_validation_same_as_create": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_valid_category_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_minimal_category_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_with_parent": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_empty": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_whitespace": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_normalization": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_valid_partial_category_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_empty_category_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_category_update_validation_same_as_create": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentReadSchema::test_read_schema_includes_all_fields": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_list_response_structure": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_empty_list_response": true}