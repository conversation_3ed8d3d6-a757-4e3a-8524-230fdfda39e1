from typing import Optional  # Import Optional

from sqlalchemy import select
from sqlalchemy.exc import NoResultFound, SQLAlchemyError
from sqlalchemy.orm import (
    Session,
    selectinload,  # Import selectinload
)

from backend.core.errors.exceptions import (
    ProjectNotFoundError,  # Import ProjectNotFoundError
)
from backend.core.models.project import Project
from backend.core.repositories.base_repository import BaseRepository


class ProjectRepository(BaseRepository[Project]):
    def __init__(self, db_session: Session):
        super().__init__(db_session, Project)

    def get_by_code(self, code: str) -> Project:
        try:
            stmt = select(self.model).where(self.model.code == code)
            return self.db_session.scalars(
                stmt
            ).one()  # Use .one() for expected single result
        except NoResultFound:
            raise ProjectNotFoundError(project_id=code)  # Specific error for clarity
        except SQLAlchemyError as e:
            self._handle_db_exception(
                e, "project"
            )  # Uses base handler for other DB errors
            raise  # Added raise

    # Example of a custom query with related objects
    def get_project_with_circuits(self, project_id: int) -> Optional[Project]:
        try:
            # Eagerly load circuits to avoid N+1 problem
            stmt = (
                select(self.model)
                .where(self.model.id == project_id)
                .options(selectinload(self.model.circuits))
            )
            return self.db_session.scalar(stmt)
        except SQLAlchemyError as e:
            self._handle_db_exception(e, "project")
            raise  # Added raise
